overwrite: true
schema: '../Shared/api/graphql/schema.graphql'
documents:
  - './src/**/*.ts'
generates:
  ./src/lib/graphql/generated.ts:
    plugins:
      - typescript
      - typescript-operations
      - introspection
    config:
      # TypeScript 配置
      enumsAsTypes: true
      constEnums: true
      numericEnums: false

      # 命名配置
      typesPrefix: ''
      typesSuffix: ''
      enumPrefix: false
      enumSuffix: 'Enum'

      # 輸出配置
      declarationKind: 'interface'
      maybeValue: 'T | null | undefined'
      inputMaybeValue: 'T | null | undefined'

      # 標量類型映射
      scalars:
        DateTime: 'string'

      # 生成運行時內省 schema
      introspection:
        minify: false

      # 避免與保留字衝突
      avoidOptionals:
        field: false
        inputValue: false
        object: false

  ./src/lib/graphql/schema.json:
    plugins:
      - introspection
    config:
      minify: true
hooks:
  afterOneFileWrite:
    - prettier --write