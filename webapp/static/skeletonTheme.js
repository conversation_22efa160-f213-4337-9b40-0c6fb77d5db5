/**
 * Skeleton UI 防閃爍腳本
 * 在頁面載入前設定正確的主題，避免主題切換閃爍
 */

(function() {
  // 可用的主題列表（與 themeStore.ts 保持同步）
  const availableThemes = [
    'cerberus',
    'modern', 
    'rocket',
    'vintage',
    'seafoam',
    'hamlindigo',
    'crimson'
  ];
  
  const DEFAULT_THEME = 'cerberus';
  const STORAGE_KEY = 'skeleton-theme';
  
  // 從 localStorage 讀取主題
  function getStoredTheme() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored && availableThemes.includes(stored)) {
        return stored;
      }
    } catch (error) {
      // localStorage 不可用
    }
    return DEFAULT_THEME;
  }
  
  // 應用主題
  function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
  }
  
  // 立即應用主題
  const storedTheme = getStoredTheme();
  applyTheme(storedTheme);
})();