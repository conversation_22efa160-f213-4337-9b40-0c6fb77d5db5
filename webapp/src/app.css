@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import '@skeletonlabs/skeleton/themes/cerberus';
@import '@skeletonlabs/skeleton';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主題切換動畫 */
:root {
  --transition-theme: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

* {
  transition: var(--transition-theme);
}

/* 防止閃爍 */
html {
  color-scheme: light dark;
}

html.dark {
  color-scheme: dark;
}

html.light {
  color-scheme: light;
}

@layer components {
  /* Skeleton UI 元件補強樣式 */
  .alert-message {
    @apply flex-1;
  }

  .alert-actions {
    @apply flex items-center gap-2;
  }

  .card-header {
    @apply pb-4;
  }

  .card-footer {
    @apply pt-4;
  }
}