<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { checkAuthStatus } from '$lib/auth';

  onMount(async () => {
    const isAuth = await checkAuthStatus();
    
    if (isAuth) {
      goto('/whendiditake');
    } else {
      goto('/login');
    }
  });
</script>

<div class="min-h-screen flex items-center justify-center bg-surface-50 dark:bg-surface-900">
  <div class="flex items-center space-x-3">
    <iconify-icon icon="lucide:loader-2" class="text-3xl text-primary-500 animate-spin"></iconify-icon>
    <span class="text-surface-600 dark:text-surface-300">載入中...</span>
  </div>
</div>
