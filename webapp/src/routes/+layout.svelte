<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { checkAuthStatus, getStoredToken, isTokenExpired, clearToken } from '$lib/auth';
  import { goto } from '$app/navigation';
  import { themeStore } from '$lib/stores/themeStore';
  import { startGlobalActivityTracking } from '$lib/activityTracker';
  import { setContextClient } from '@urql/svelte';
  import { getUrqlClient } from '$lib/graphql/client';
  import '../app.css';

  // 設定全域 urql 客戶端
  setContextClient(getUrqlClient());

  let isReady = false;
  let showLogoutMessage = false;
  let logoutCountdown = 5;

  onMount(async () => {
    // 初始化主題
    themeStore.init();
    
    // 啟動全域活動追蹤
    const activityTrackingCleanup = startGlobalActivityTracking();
    
    const path = $page.url.pathname;
    
    // 檢查當前認證狀態
    const isAuth = await checkAuthStatus();
    
    if (path === '/login') {
      // 如果在登入頁面且已認證，跳轉到主頁面
      if (isAuth) {
        goto('/whendiditake');
      }
    } else {
      // 如果不在登入頁面但未認證，檢查是否有過期的token
      if (!isAuth) {
        const storedToken = getStoredToken();
        if (storedToken && isTokenExpired(storedToken)) {
          // JWT過期，顯示登出訊息和倒數計時
          clearToken();
          showLogoutMessage = true;
          
          const countdownInterval = setInterval(() => {
            logoutCountdown--;
            if (logoutCountdown <= 0) {
              clearInterval(countdownInterval);
              goto('/login');
            }
          }, 1000);
        } else {
          // 無JWT，直接跳轉到登入頁面
          goto('/login');
        }
      }
    }
    
    isReady = true;
    
    // 返回清理函數
    return () => {
      if (activityTrackingCleanup) {
        activityTrackingCleanup();
      }
    };
  });

  function handleLoginNow() {
    showLogoutMessage = false;
    goto('/login');
  }
</script>

{#if isReady}
  <main class="min-h-screen bg-surface-50 dark:bg-surface-900">
    {#if showLogoutMessage}
      <div class="min-h-screen flex items-center justify-center">
        <div class="card variant-filled-surface p-8 max-w-sm mx-auto">
          <header class="card-header text-center mb-4">
            <h2 class="h2 text-primary-500">已登出</h2>
          </header>
          <section class="p-4 text-center">
            <p class="mb-4">您的登入已過期</p>
            <p class="text-sm opacity-70 mb-6">
              {logoutCountdown} 秒後將自動跳轉至登入頁面
            </p>
            <button 
              class="btn variant-filled-primary"
              on:click={handleLoginNow}
            >
              立即登入
            </button>
          </section>
        </div>
      </div>
    {:else}
      <slot />
    {/if}
  </main>
{/if}