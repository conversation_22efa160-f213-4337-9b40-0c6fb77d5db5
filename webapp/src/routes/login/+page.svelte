<script lang="ts">
  import { goto } from '$app/navigation';
  import { login, saveToken, type LoginRequest } from '$lib/auth';
  import { rememberMeService } from '$lib/services/rememberMeService';
  import { dataManager } from '$lib/services/dataManager';
  import { onMount } from 'svelte';

  let userName = '';
  let password = '';
  let isLoading = false;
  let errorMessage = '';
  let rememberMe = false; // 記住我選項

  // 密碼顯示控制
  let showPassword = false; // 預設隱藏

  onMount(() => {
    // 清除之前的錯誤訊息
    errorMessage = '';
    
    // 載入記住的登入資訊
    loadRememberedCredentials();
  });

  function loadRememberedCredentials() {
    const remembered = rememberMeService.load();
    if (remembered && remembered.rememberMe) {
      userName = remembered.userName || '';
      rememberMe = true;
    }
  }

  function saveRememberedCredentials() {
    rememberMeService.save({
      userName: userName,
      rememberMe: rememberMe
    });
  }

  async function handleLogin() {
    if (!userName || !password) {
      errorMessage = '請輸入使用者名稱和密碼';
      return;
    }

    // 檢查 userName 是否為數字
    const userId = parseInt(userName);
    if (isNaN(userId)) {
      errorMessage = '使用者名稱必須是數字';
      return;
    }

    isLoading = true;
    errorMessage = '';

    try {
      const credentials: LoginRequest = {
        user_id: userId,
        password
      };

      const response = await login(credentials);

      // 儲存 JWT
      saveToken(response.access_token);

      // 保存記住的登入資訊
      saveRememberedCredentials();

      // 開始載入基礎資料（在背景進行）
      console.log('登入成功，開始載入使用者資料...');
      dataManager.loadUserInfo().then(() => {
        console.log('使用者資料載入成功');
      }).catch(error => {
        console.error('載入使用者資料失敗:', error);
      });
      
      dataManager.loadPharmaceuticals().then(() => {
        console.log('藥物清單載入成功');
      }).catch(error => {
        console.error('載入藥物清單失敗:', error);
      });
      
      dataManager.loadTakeRecords().then(() => {
        console.log('服藥記錄載入成功');
      }).catch(error => {
        console.error('載入服藥記錄失敗:', error);
      });

      // 立即跳轉到主頁面
      goto('/whendiditake');
    } catch (error) {
      if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = '登入失敗，請稍後重試';
      }
    } finally {
      isLoading = false;
    }
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleLogin();
    }
  }

  // 切換密碼顯示/隱藏
  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }
</script>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 via-secondary-50 to-surface-50 relative overflow-hidden">
  <!-- Skeleton UI 背景裝飾 -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-20 left-20 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
    <div class="absolute top-40 right-20 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
    <div class="absolute -bottom-8 left-1/2 w-72 h-72 bg-primary-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-500"></div>
  </div>

  <div class="relative z-10 w-full max-w-md mx-4">
    <!-- Skeleton UI 主卡片 -->
    <div class="card p-8 backdrop-blur-lg shadow-2xl">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="mb-6 relative">
          <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-600 rounded-2xl mx-auto flex items-center justify-center shadow-lg">
            <iconify-icon icon="lucide:pill" class="text-3xl text-white"></iconify-icon>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full animate-ping"></div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full"></div>
        </div>
        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
          When Did I Take
        </h1>
        <p class="text-gray-600 dark:text-gray-300 text-sm">安全登入您的用藥管理系統</p>
      </div>

      <!-- Login Form -->
      <div class="space-y-6">
        <!-- Skeleton UI Username Field -->
        <label class="label">
          <span>使用者 ID</span>
          <div class="input-group input-group-divider grid-cols-[auto_1fr]">
            <div class="input-group-shim">
              <iconify-icon icon="lucide:user" class="text-lg"></iconify-icon>
            </div>
            <input
              id="username"
              type="text"
              class="input"
              placeholder="請輸入使用者 ID"
              bind:value={userName}
              on:keypress={handleKeyPress}
              disabled={isLoading}
              autocomplete="username"
            />
          </div>
        </label>

        <!-- Skeleton UI Password Field -->
        <label class="label">
          <span>密碼</span>
          <div class="input-group input-group-divider grid-cols-[auto_1fr_auto]">
            <div class="input-group-shim">
              <iconify-icon icon="lucide:lock" class="text-lg"></iconify-icon>
            </div>
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              class="input"
              placeholder="請輸入密碼"
              bind:value={password}
              on:keypress={handleKeyPress}
              disabled={isLoading}
              autocomplete="current-password"
            />
            <button
              type="button"
              class="input-group-shim hover:variant-soft-primary"
              on:click={togglePasswordVisibility}
              disabled={isLoading}
              aria-label={showPassword ? "隱藏密碼" : "顯示密碼"}
            >
              <iconify-icon
                icon={showPassword ? "lucide:eye-off" : "lucide:eye"}
                class="text-lg"
              ></iconify-icon>
            </button>
          </div>
        </label>

        <!-- Skeleton UI Remember Me Checkbox -->
        <div class="flex items-center justify-between">
          <label class="flex items-center space-x-2">
            <input
              id="rememberMe"
              type="checkbox"
              class="checkbox"
              bind:checked={rememberMe}
              disabled={isLoading}
            />
            <span class="text-sm font-medium">記住我</span>
          </label>
          <button
            type="button"
            class="btn btn-sm variant-ghost-primary"
            disabled={isLoading}
          >
            忘記密碼？
          </button>
        </div>

        <!-- Skeleton UI Error Message -->
        {#if errorMessage}
          <aside class="alert variant-filled-error">
            <div class="alert-message">
              <iconify-icon icon="lucide:alert-circle" class="text-xl"></iconify-icon>
              <p class="font-medium">{errorMessage}</p>
            </div>
          </aside>
        {/if}

        <!-- Skeleton UI Login Button -->
        <button
          class="btn variant-filled-primary w-full"
          disabled={isLoading}
          on:click={handleLogin}
        >
          {#if isLoading}
            <iconify-icon icon="lucide:loader-2" class="text-xl animate-spin"></iconify-icon>
            <span>登入中...</span>
          {:else}
            <iconify-icon icon="lucide:log-in" class="text-xl"></iconify-icon>
            <span>登入</span>
          {/if}
        </button>
      </div>
    </div>

    <!-- Skeleton UI 浮動提示 -->
    <div class="mt-6 text-center">
      <aside class="alert variant-ghost-primary inline-flex">
        <iconify-icon icon="lucide:info" class="text-sm"></iconify-icon>
        <div class="alert-message">
          <span class="text-xs font-medium">使用您的用戶ID和密碼登入</span>
        </div>
      </aside>
    </div>
  </div>
</div>

