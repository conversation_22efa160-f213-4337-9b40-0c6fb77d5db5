<script lang="ts">
  import Navigation from '$lib/components/Navigation.svelte';
  import QuickTakeOption from './components/QuickTakeOption.svelte';
  import TakeRecord from './components/TakeRecord.svelte';
  import { onMount, onDestroy } from 'svelte';
  import { flip } from 'svelte/animate';
  import { takeRecordService } from '$lib/services/takeRecordService';
  import { quickOptionsService } from './services/quickOptionsService';
  import type { GraphQLTakeType } from '$lib/graphql/generated';
  import { dataManager } from '$lib/services/dataManager';
  import { takeRecordsStore, pharmaceuticalsStore } from '$lib/stores/dataStore';
  import { LoadingState, shouldShowLoader, shouldShowSkeleton } from '$lib/utils/loadingHelpers';
  import LoadingStateComponent from '$lib/components/LoadingState.svelte';
  import { format, compareDesc, parseISO } from 'date-fns';
  import { zhTW } from 'date-fns/locale';

  // 訂閱頁面級快速選項服務
  $: quickOptions = quickOptionsService.options;

  let showScrollToTop = false;
  let scrollContainer: HTMLElement;
  let errorMessage = '';
  let showError = false;
  
  // 刪除確認對話框
  let showDeleteConfirm = false;
  let recordToDelete: GraphQLTakeType | null = null;
  let isDeleting = false;
  let deletingRecordIds = new Set<number>();
  
  // 編輯對話框
  let showEditDialog = false;
  let recordToEdit: GraphQLTakeType | null = null;
  let isEditing = false;
  let editForm = {
    quantity: 0,
    takenAt: '',
    notes: ''
  };
  
  function handleTimeChange(e: Event) {
     if (recordToEdit && e.target) {
       const target = e.target as HTMLInputElement;
       if (target.value) {
         const [date, time] = target.value.split('T');
         const originalDate = new Date(recordToEdit.takenAt);
         const originalTimeString = originalDate.toTimeString();
         
         // 如果只修改了日期，保留原始時間
         if (time === undefined) {
           const [hours, minutes] = originalTimeString.split(':');
           editForm.takenAt = `${date}T${hours}:${minutes}:00.000Z`;
         } else {
           editForm.takenAt = `${date}T${time}:00.000Z`;
         }
       }
     }
   }

  // 監聽滾動事件
  function handleScroll() {
    if (!scrollContainer) return;
    
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    
    // 顯示/隱藏回到頂部按鈕
    showScrollToTop = scrollTop > 300;
    
    // 檢查是否需要載入更多資料
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      loadMoreRecords();
    }
  }

  // 載入更多歷史紀錄
  async function loadMoreRecords() {
    // 註：需要重新實現 loading 和 pagination 邏輯
    
    try {
      // 註：loadMorePharmaceuticalTakeRecords 已移除，需要從 dataManager 重新實現
    } catch (error) {
      console.error('載入更多紀錄失敗:', error);
    }
  }

  // 回到頂部
  function scrollToTop() {
    if (scrollContainer) {
      scrollContainer.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  // 處理快速用藥新增
  function handleQuickPharmaceuticalAdded(event: CustomEvent) {
    console.log('快速用藥已新增:', event.detail);
  }

  function handleQuickPharmaceuticalError(event: CustomEvent) {
    console.error('快速用藥失敗:', event.detail.error);
    showErrorMessage(event.detail.error);
  }

  function showErrorMessage(message: string) {
    errorMessage = message;
    showError = true;
    // 3秒後自動隱藏錯誤提示
    setTimeout(() => {
      showError = false;
    }, 5000);
  }

  function hideError() {
    showError = false;
  }

  // 處理用藥紀錄編輯和刪除
  function handlePharmaceuticalEdit(event: CustomEvent) {
    recordToEdit = event.detail;
    if (recordToEdit) {
      editForm = {
        quantity: recordToEdit.quantity,
        takenAt: recordToEdit.takenAt,
        notes: recordToEdit.notes || ''
      };
      showEditDialog = true;
    }
  }

  function handlePharmaceuticalDelete(event: CustomEvent) {
    recordToDelete = event.detail;
    showDeleteConfirm = true;
  }

  // 確認刪除
  async function confirmDelete() {
    if (!recordToDelete) return;
    
    try {
      isDeleting = true;
      const recordIdToDelete = recordToDelete.id;
      deletingRecordIds.add(recordIdToDelete);
      deletingRecordIds = deletingRecordIds; // 觸發反應性更新
      
      // 關閉對話框
      showDeleteConfirm = false;
      recordToDelete = null;
      
      // 先標記為刪除中，讓動畫開始
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // 執行刪除操作
      const success = await takeRecordService.deleteTakeRecord(recordIdToDelete);
      
      // 等待動畫完成
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (success) {
        showErrorMessage('用藥紀錄已成功刪除');
      } else {
        showErrorMessage('刪除失敗，請稍後重試');
        // 如果刪除失敗，移除刪除標記
        deletingRecordIds.delete(recordIdToDelete);
        deletingRecordIds = deletingRecordIds;
      }
    } catch (error) {
      console.error('刪除用藥紀錄失敗:', error);
      showErrorMessage(error instanceof Error ? error.message : '刪除失敗，請稍後重試');
    } finally {
      isDeleting = false;
      // 等待一段時間後清除刪除狀態
      setTimeout(() => {
        deletingRecordIds.clear();
        deletingRecordIds = deletingRecordIds;
      }, 400);
    }
  }

  // 取消刪除
  function cancelDelete() {
    showDeleteConfirm = false;
    recordToDelete = null;
  }

  // 確認編輯
  async function confirmEdit() {
    if (!recordToEdit) return;
    
    try {
      isEditing = true;
      
      const updateData = {
        id: recordToEdit.id,
        quantity: editForm.quantity,
        takenAt: editForm.takenAt,
        notes: editForm.notes || undefined
      };
      
      await takeRecordService.updateTakeRecord(updateData);
      
      showEditDialog = false;
      recordToEdit = null;
      showErrorMessage('用藥紀錄已成功更新');
    } catch (error) {
      console.error('更新用藥紀錄失敗:', error);
      showErrorMessage(error instanceof Error ? error.message : '更新失敗，請稍後重試');
    } finally {
      isEditing = false;
    }
  }

  // 取消編輯
  function cancelEdit() {
    showEditDialog = false;
    recordToEdit = null;
  }

  onMount(async () => {
    // 檢查並載入資料（如果尚未載入）
    try {
      console.log('=== 主頁面載入開始 ===');
      console.log('藥物清單狀態:', $pharmaceuticalsStore);
      console.log('服藥記錄狀態:', $takeRecordsStore);
      console.log('快速選項:', $quickOptions.data);
      
      // 如果 DataManager 還沒有載入資料，則載入
      if (!$pharmaceuticalsStore.data && $pharmaceuticalsStore.loading.state === LoadingState.NOT_LOADED) {
        console.log('開始載入藥物清單...');
        await dataManager.loadPharmaceuticals();
        console.log('藥物清單載入完成:', $pharmaceuticalsStore.data);
      }
      if (!$takeRecordsStore.data && $takeRecordsStore.loading.state === LoadingState.NOT_LOADED) {
        console.log('開始載入服藥記錄...');
        await dataManager.loadTakeRecords();
        console.log('服藥記錄載入完成:', $takeRecordsStore.data);
      }
      
      // 載入快速選項
      if ($pharmaceuticalsStore.data && $takeRecordsStore.data) {
        console.log('開始載入快速選項...');
        await quickOptionsService.loadQuickOptions($takeRecordsStore.data, $pharmaceuticalsStore.data);
        console.log('快速選項載入完成:', $quickOptions.data);
      }
      
      console.log('=== 主頁面載入完成 ===');
    } catch (error) {
      console.error('載入初始資料失敗:', error);
      errorMessage = `載入資料失敗: ${error.message}`;
      showError = true;
    }
  });

  onDestroy(() => {
    // 清理事件監聽器
  });

  // 根據日期分組的用藥紀錄
  $: groupedRecords = ($takeRecordsStore.data || []).reduce((groups, record) => {
    const dateObj = typeof record.takenAt === 'string' ? parseISO(record.takenAt) : record.takenAt;
    const date = format(dateObj, 'yyyy/MM/dd', { locale: zhTW });
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(record);
    return groups;
  }, {} as Record<string, GraphQLTakeType[]>);

  // 按日期排序的群組鍵
  $: sortedDateKeys = Object.keys(groupedRecords).sort((a, b) => 
    compareDesc(parseISO(a.replace(/\//g, '-')), parseISO(b.replace(/\//g, '-')))
  );
</script>

<div class="min-h-screen bg-surface-50 dark:bg-surface-900">
  <!-- 固定導航列 -->
  <div class="sticky top-0 z-50">
    <Navigation />
  </div>

  <!-- 錯誤提示 -->
  {#if showError}
    <div class="fixed top-20 left-1/2 transform -translate-x-1/2 z-40 max-w-md w-full mx-4">
      <aside class="alert variant-filled-error shadow-lg">
        <iconify-icon icon="lucide:alert-circle" class="text-xl"></iconify-icon>
        <div class="alert-message">
          <span class="font-medium">{errorMessage}</span>
        </div>
        <div class="alert-actions">
          <button 
            class="btn-icon btn-icon-sm variant-filled"
            on:click={hideError}
            aria-label="關閉錯誤訊息"
          >
            <iconify-icon icon="lucide:x" class="text-sm"></iconify-icon>
          </button>
        </div>
      </aside>
    </div>
  {/if}

  <!-- 主要內容區域 -->
  <div 
    class="h-screen overflow-y-auto"
    bind:this={scrollContainer}
    on:scroll={handleScroll}
  >
    <main class="container mx-auto px-4 pt-20 pb-6 space-y-6">
      <!-- 快速新增區塊 -->
      <section class="bg-surface-50 dark:bg-surface-800 rounded-lg shadow-sm border border-surface-200 dark:border-surface-700">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <iconify-icon icon="lucide:zap" class="text-xl text-green-500"></iconify-icon>
            <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-100">快速服藥</h2>
          </div>
          
          {#if shouldShowLoader($quickOptions.loading) || shouldShowSkeleton($quickOptions.loading)}
            <LoadingStateComponent 
              state={$quickOptions.loading} 
              error={$quickOptions.error}
              skeletonType="card"
              itemCount={3}
              retryCallback={() => {
                if ($pharmaceuticalsStore.data && $takeRecordsStore.data) {
                  quickOptionsService.loadQuickOptions($takeRecordsStore.data, $pharmaceuticalsStore.data);
                }
              }}
            />
          {:else if ($quickOptions.data?.length || 0) === 0}
            <div class="text-center py-8 text-surface-500 dark:text-surface-400">
              <iconify-icon icon="lucide:pill" class="text-3xl mb-2"></iconify-icon>
              <p>尚無快速服藥選項</p>
              <p class="text-sm mt-1">建立一些用藥紀錄後，系統會自動生成常用的快速選項</p>
            </div>
          {:else}
            <div class="grid gap-3 md:grid-cols-2">
              {#each ($quickOptions.data || []) as option}
                <QuickTakeOption 
                  {option}
                  on:added={handleQuickPharmaceuticalAdded}
                  on:error={handleQuickPharmaceuticalError}
                />
              {/each}
            </div>
          {/if}
        </div>
      </section>

      <!-- 用藥紀錄區塊 -->
      <section class="bg-surface-50 dark:bg-surface-800 rounded-lg shadow-sm border border-surface-200 dark:border-surface-700">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <iconify-icon icon="lucide:history" class="text-xl text-blue-500"></iconify-icon>
              <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-100">用藥紀錄</h2>
            </div>
            <span class="text-sm text-surface-500 dark:text-surface-400">
              {($takeRecordsStore.data?.length || 0)} 筆紀錄
            </span>
          </div>

          {#if shouldShowLoader($takeRecordsStore.loading.state) || shouldShowSkeleton($takeRecordsStore.loading.state)}
            <LoadingStateComponent 
              state={$takeRecordsStore.loading.state} 
              error={$takeRecordsStore.loading.error}
              skeletonType="list"
              itemCount={5}
              retryCallback={() => dataManager.loadTakeRecords({}, true)}
            />
          {:else if ($takeRecordsStore.data?.length || 0) === 0}
            <div class="text-center py-12 text-surface-500 dark:text-surface-400">
              <iconify-icon icon="lucide:calendar-x" class="text-4xl mb-3"></iconify-icon>
              <p class="text-lg mb-2">尚無用藥紀錄</p>
              <p class="text-sm">開始記錄您的用藥情況吧！</p>
            </div>
          {:else}
            <div class="space-y-6">
              {#each sortedDateKeys as dateKey}
                <div class="space-y-3">
                  <!-- 日期標題 -->
                  <div class="flex items-center space-x-2 py-2 border-b border-surface-200 dark:border-surface-600">
                    <iconify-icon icon="lucide:calendar" class="text-sm text-surface-400"></iconify-icon>
                    <h3 class="font-medium text-surface-700 dark:text-surface-300">{dateKey}</h3>
                    <span class="text-xs text-surface-400">({groupedRecords[dateKey].length} 次)</span>
                  </div>
                  
                  <!-- 該日期的用藥紀錄 -->
                  <div class="space-y-2">
                    {#each groupedRecords[dateKey].sort((a, b) => {
                      const d1 = typeof a.takenAt === 'string' ? parseISO(a.takenAt) : a.takenAt;
                      const d2 = typeof b.takenAt === 'string' ? parseISO(b.takenAt) : b.takenAt;
                      return compareDesc(d1, d2);
                    }) as record (record.id)}
                      <div animate:flip={{ duration: 300 }}>
                        <TakeRecord 
                          {record}
                          isDeleting={deletingRecordIds.has(record.id)}
                          on:edit={handlePharmaceuticalEdit}
                          on:delete={handlePharmaceuticalDelete}
                        />
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}

              <!-- 載入更多指示器 -->
              {#if false}
                <div class="flex items-center justify-center py-6">
                  <iconify-icon icon="lucide:loader-2" class="text-xl text-surface-400 animate-spin"></iconify-icon>
                  <span class="ml-2 text-surface-500">載入更多...</span>
                </div>
              {:else if true}
                <div class="text-center py-6 text-surface-400 dark:text-surface-500">
                  <iconify-icon icon="lucide:check-circle" class="text-xl"></iconify-icon>
                  <p class="mt-2 text-sm">已載入所有紀錄</p>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      </section>
    </main>
  </div>

  <!-- Skeleton UI 回到頂部按鈕 -->
  {#if showScrollToTop}
    <button
      class="btn-icon variant-filled-primary fixed bottom-6 right-6 z-40 !p-3 shadow-lg"
      on:click={scrollToTop}
      aria-label="回到頂部"
    >
      <iconify-icon icon="lucide:arrow-up" class="text-xl"></iconify-icon>
    </button>
  {/if}
</div>

<!-- 刪除確認對話框 -->
{#if showDeleteConfirm && recordToDelete}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" role="dialog" aria-labelledby="delete-title">
    <div class="bg-surface-50 dark:bg-surface-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0">
          <iconify-icon icon="lucide:trash-2" class="text-2xl text-red-500"></iconify-icon>
        </div>
        <div>
          <h3 id="delete-title" class="text-lg font-semibold text-surface-900 dark:text-surface-100">
            確認刪除用藥紀錄
          </h3>
        </div>
      </div>
      
      <div class="mb-6">
        <p class="text-surface-600 dark:text-surface-300 mb-3">
          您確定要刪除這筆用藥紀錄嗎？
        </p>
        <div class="bg-surface-50 dark:bg-surface-700 rounded-lg p-3 space-y-2">
          <p class="text-sm"><strong>藥物：</strong>{recordToDelete.pharmaceutical?.name}</p>
          <p class="text-sm"><strong>劑量：</strong>{recordToDelete.quantity} {recordToDelete.pharmaceutical?.dosageUnit}</p>
          <p class="text-sm"><strong>服用時間：</strong>{format(typeof recordToDelete.takenAt === 'string' ? parseISO(recordToDelete.takenAt) : recordToDelete.takenAt, 'yyyy/MM/dd HH:mm', { locale: zhTW })}</p>
          {#if recordToDelete.notes}
            <p class="text-sm"><strong>註記：</strong>{recordToDelete.notes}</p>
          {/if}
        </div>
        <p class="text-sm text-red-600 dark:text-red-400 mt-2">
          此操作無法復原。
        </p>
      </div>

      <div class="flex space-x-3 justify-end">
        <button
          type="button"
          class="btn variant-ghost-surface"
          on:click={cancelDelete}
          disabled={isDeleting}
        >
          取消
        </button>
        <button
          type="button"
          class="btn variant-filled-error"
          on:click={confirmDelete}
          disabled={isDeleting}
        >
          {#if isDeleting}
            <iconify-icon icon="lucide:loader-2" class="text-sm animate-spin"></iconify-icon>
            <span>刪除中...</span>
          {:else}
            <iconify-icon icon="lucide:trash-2" class="text-sm"></iconify-icon>
            <span>確認刪除</span>
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- 編輯對話框 -->
{#if showEditDialog && recordToEdit}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" role="dialog" aria-labelledby="edit-title">
    <div class="bg-surface-50 dark:bg-surface-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0">
          <iconify-icon icon="lucide:edit-2" class="text-2xl text-blue-500"></iconify-icon>
        </div>
        <div>
          <h3 id="edit-title" class="text-lg font-semibold text-surface-900 dark:text-surface-100">
            編輯用藥紀錄
          </h3>
        </div>
      </div>
      
      <div class="space-y-4 mb-6">
        <!-- 藥物名稱（只讀） -->
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            藥物名稱
          </label>
          <div class="p-3 bg-surface-100 dark:bg-surface-700 rounded-lg text-surface-600 dark:text-surface-300">
            {recordToEdit.pharmaceutical?.name}
          </div>
        </div>

        <!-- 劑量 -->
        <div>
          <label for="edit-quantity" class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            劑量
          </label>
          <div class="flex items-center space-x-2">
            <input
              id="edit-quantity"
              type="number"
              min="0.1"
              step="0.1"
              bind:value={editForm.quantity}
              class="flex-1 p-3 border border-surface-300 dark:border-surface-600 rounded-lg bg-surface-50 dark:bg-surface-700 text-surface-900 dark:text-surface-100"
              disabled={isEditing}
            />
            <span class="text-surface-600 dark:text-surface-400">{recordToEdit.pharmaceutical?.dosageUnit}</span>
          </div>
        </div>

        <!-- 服用時間 -->
        <div>
          <label for="edit-taken-at" class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            服用時間
          </label>
          <input
              id="edit-taken-at"
              type="datetime-local"
              value={editForm.takenAt ? editForm.takenAt.slice(0, 16) : ''}
              on:input={handleTimeChange}
              class="w-full p-3 border border-surface-300 dark:border-surface-600 rounded-lg bg-surface-50 dark:bg-surface-700 text-surface-900 dark:text-surface-100"
              disabled={isEditing}
            />
        </div>

        <!-- 註記 -->
        <div>
          <label for="edit-notes" class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            註記
          </label>
          <textarea
            id="edit-notes"
            bind:value={editForm.notes}
            rows="3"
            class="w-full p-3 border border-surface-300 dark:border-surface-600 rounded-lg bg-surface-50 dark:bg-surface-700 text-surface-900 dark:text-surface-100"
            placeholder="選填..."
            disabled={isEditing}
          ></textarea>
        </div>
      </div>

      <div class="flex space-x-3 justify-end">
        <button
          type="button"
          class="btn variant-ghost-surface"
          on:click={cancelEdit}
          disabled={isEditing}
        >
          取消
        </button>
        <button
          type="button"
          class="btn variant-filled-primary flex items-center space-x-2"
          on:click={confirmEdit}
          disabled={isEditing || editForm.quantity <= 0}
        >
          {#if isEditing}
            <iconify-icon icon="lucide:loader-2" class="text-sm animate-spin"></iconify-icon>
            <span>更新中...</span>
          {:else}
            <iconify-icon icon="lucide:save" class="text-sm"></iconify-icon>
            <span>儲存變更</span>
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

