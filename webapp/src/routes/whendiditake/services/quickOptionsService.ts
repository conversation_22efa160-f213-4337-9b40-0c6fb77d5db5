/**
 * 頁面級快速選項服務 - 專注於 whendiditake 頁面的快速選項邏輯
 * 整合所有快速服用相關功能，包含型別定義、業務邏輯、狀態管理
 * 最後更新: 2025/7/22
 */

import { writable, type Writable } from 'svelte/store';
import type { GraphQLTakeType, GraphQLPharmaceuticalType } from '$lib/graphql/generated';
import { takeRecordService } from '$lib/services/takeRecordService';
import { LoadingState } from '$lib/utils/loadingHelpers';
import { isSameDay, compareDesc, parseISO } from 'date-fns';

// ==================== 型別定義 ====================

// 快速服用選項 - 組合藥物基本資訊和使用統計
export interface QuickTakeOption {
  pharmaceutical: Pick<GraphQLPharmaceuticalType, 'id' | 'name' | 'dosagePerServing' | 'dosageUnit' | 'servingUnitName'>;
  usageStats: {
    dosage: number;          // 統計分析的慣用劑量（關鍵欄位）
    totalCount: number;      // 總服用次數
    todayCount: number;      // 今日服用次數  
    todayDosage: number;     // 今日總劑量
    lastTakenAt: string;     // 最後服用時間
  };
}

// 工廠函數 - 明確表達轉換關係
export function createQuickTakeOption(
  pharmaceutical: GraphQLPharmaceuticalType,
  usageStats: {
    dosage: number;
    totalCount: number;
    todayCount: number;
    todayDosage: number;
    lastTakenAt: string;
  }
): QuickTakeOption {
  return {
    pharmaceutical: {
      id: pharmaceutical.id,
      name: pharmaceutical.name,
      dosagePerServing: pharmaceutical.dosagePerServing,
      dosageUnit: pharmaceutical.dosageUnit,
      servingUnitName: pharmaceutical.servingUnitName
    },
    usageStats
  };
}

// 快速選項載入狀態
interface QuickOptionsState {
  data: QuickTakeOption[];
  loading: LoadingState;
  error?: string;
  lastUpdate?: Date;
}

// ==================== 服務類 ====================

/**
 * 快速選項服務類 - 專注於快速選項生成邏輯和狀態管理
 */
export class QuickOptionsService {
  private static instance: QuickOptionsService;
  private optionsStore: Writable<QuickOptionsState>;

  private constructor() {
    this.optionsStore = writable<QuickOptionsState>({
      data: [],
      loading: LoadingState.NOT_LOADED
    });
  }

  /**
   * 取得單例實例
   */
  static getInstance(): QuickOptionsService {
    if (!QuickOptionsService.instance) {
      QuickOptionsService.instance = new QuickOptionsService();
    }
    return QuickOptionsService.instance;
  }

  /**
   * 取得快速選項 Store（供組件訂閱）
   */
  get options() {
    return this.optionsStore;
  }

  /**
   * 載入快速選項
   */
  async loadQuickOptions(
    takeRecords: GraphQLTakeType[], 
    pharmaceuticals: GraphQLPharmaceuticalType[]
  ): Promise<QuickTakeOption[]> {
    this.setLoadingState(LoadingState.LOADING);

    try {
      const options = this.generateQuickOptions(takeRecords, pharmaceuticals);
      
      this.optionsStore.update(state => ({
        ...state,
        data: options,
        loading: LoadingState.LOADED,
        error: undefined,
        lastUpdate: new Date()
      }));

      return options;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '載入快速選項失敗';
      
      this.optionsStore.update(state => ({
        ...state,
        loading: LoadingState.ERROR,
        error: errorMessage
      }));

      throw error;
    }
  }

  /**
   * 生成快速選項（整合兩套邏輯的最佳版本）
   */
  private generateQuickOptions(
    takeRecords: GraphQLTakeType[], 
    pharmaceuticals: GraphQLPharmaceuticalType[]
  ): QuickTakeOption[] {
    // 取得治療中的藥物 ID 集合
    const currentPlanPharmaceuticals = new Set(
      pharmaceuticals.filter(p => p.currentPlan).map(p => p.id)
    );
    
    // 過濾只包含治療中的藥物
    const authorizedRecords = takeRecords.filter(record => 
      currentPlanPharmaceuticals.has(record.pharmaceuticalId)
    );
    
    // 按藥物 ID 和劑量分組
    const pharmaceuticalGroups = new Map<string, {
      pharmaceuticalId: number;
      pharmaceuticalName: string;
      dosage: number;
      dosageUnit: string;
      records: GraphQLTakeType[];
    }>();

    authorizedRecords.forEach(record => {
      const key = `${record.pharmaceuticalId}-${record.quantity}`;
      const pharmaceutical = pharmaceuticals.find(p => p.id === record.pharmaceuticalId);

      if (!pharmaceutical) return;

      if (!pharmaceuticalGroups.has(key)) {
        pharmaceuticalGroups.set(key, {
          pharmaceuticalId: record.pharmaceuticalId,
          pharmaceuticalName: pharmaceutical.name,
          dosage: record.quantity,
          dosageUnit: pharmaceutical.dosageUnit,
          records: []
        });
      }

      pharmaceuticalGroups.get(key)!.records.push(record);
    });

    // 找出每種藥物最常用的劑量
    const pharmaceuticalMap = new Map<number, {
      pharmaceuticalId: number;
      pharmaceuticalName: string;
      bestDosage: number;
      dosageUnit: string;
      maxCount: number;
      todayCount: number;
      lastTakenAt: string;
    }>();

    for (const [key, group] of pharmaceuticalGroups) {
      const count = group.records.length;
      
      // 計算當日服用次數
      const todayCount = group.records.filter(record => {
        const recordDate = typeof record.takenAt === 'string' ? parseISO(record.takenAt) : record.takenAt;
        return isSameDay(recordDate, new Date());
      }).length;
      
      // 取得最後服用時間
      const sortedRecords = group.records.sort((a, b) => {
        const d1 = typeof a.takenAt === 'string' ? parseISO(a.takenAt) : a.takenAt;
        const d2 = typeof b.takenAt === 'string' ? parseISO(b.takenAt) : b.takenAt;
        return compareDesc(d1, d2);
      });
      const lastTakenAt = sortedRecords[0]?.takenAt || '';

      const pharmaceuticalId = group.pharmaceuticalId;
      const existing = pharmaceuticalMap.get(pharmaceuticalId);
      
      if (!existing || count > existing.maxCount) {
        pharmaceuticalMap.set(pharmaceuticalId, {
          pharmaceuticalId: group.pharmaceuticalId,
          pharmaceuticalName: group.pharmaceuticalName,
          bestDosage: group.dosage,
          dosageUnit: group.dosageUnit,
          maxCount: count,
          todayCount,
          lastTakenAt
        });
      }
    }

    // 轉換為快速選項格式
    return Array.from(pharmaceuticalMap.values())
      .map(item => {
        const pharmaceutical = pharmaceuticals.find(p => p.id === item.pharmaceuticalId);
        if (!pharmaceutical) return null;

        // 計算今日總劑量
        const todayRecords = takeRecords.filter(record => {
          if (record.pharmaceuticalId !== item.pharmaceuticalId) return false;
          const recordDate = typeof record.takenAt === 'string' ? parseISO(record.takenAt) : record.takenAt;
          return isSameDay(recordDate, new Date());
        });
        const todayDosage = todayRecords.reduce((sum, record) => sum + record.quantity, 0);

        return createQuickTakeOption(pharmaceutical, {
          dosage: item.bestDosage,
          totalCount: item.maxCount,
          todayCount: item.todayCount,
          todayDosage,
          lastTakenAt: item.lastTakenAt
        });
      })
      .filter((item): item is QuickTakeOption => item !== null)
      .sort((a, b) => b.usageStats.totalCount - a.usageStats.totalCount);
  }

  /**
   * 快速新增服藥記錄（委託給全域服務）
   */
  async quickAddTakeRecord(option: QuickTakeOption, notes?: string): Promise<GraphQLTakeType> {
    return await takeRecordService.quickAddTakeRecord(
      option.pharmaceutical.id,
      option.usageStats.dosage,
      notes || '快速服用'
    );
  }

  /**
   * 檢查近期服用（委託給全域服務）
   */
  async checkRecentTake(pharmaceuticalId: number) {
    return await takeRecordService.checkRecentTake(pharmaceuticalId);
  }

  /**
   * 取得建議劑量
   */
  getRecommendedDosage(pharmaceuticalId: number, takeRecords: GraphQLTakeType[]): number {
    const records = takeRecords.filter(record => 
      record.pharmaceuticalId === pharmaceuticalId
    );
    
    if (records.length === 0) {
      return 0;
    }
    
    // 按劑量分組並計算頻率
    const dosageFrequency = new Map<number, number>();
    
    records.forEach(record => {
      const dosage = record.quantity;
      dosageFrequency.set(dosage, (dosageFrequency.get(dosage) || 0) + 1);
    });
    
    // 找出最常用的劑量
    let maxFrequency = 0;
    let recommendedDosage = 0;
    
    for (const [dosage, frequency] of dosageFrequency.entries()) {
      if (frequency > maxFrequency) {
        maxFrequency = frequency;
        recommendedDosage = dosage;
      }
    }
    
    return recommendedDosage;
  }

  /**
   * 計算使用頻率
   */
  calculateUsageFrequency(pharmaceuticalId: number, takeRecords: GraphQLTakeType[]): number {
    return takeRecords.filter(record => 
      record.pharmaceuticalId === pharmaceuticalId
    ).length;
  }

  /**
   * 取得當日使用次數
   */
  getTodayUsageCount(pharmaceuticalId: number, takeRecords: GraphQLTakeType[]): number {
    const records = takeRecords.filter(record => 
      record.pharmaceuticalId === pharmaceuticalId
    );
    
    return records.filter(record => {
      const recordDate = typeof record.takenAt === 'string' ? parseISO(record.takenAt) : record.takenAt;
      return isSameDay(recordDate, new Date());
    }).length;
  }

  /**
   * 設定載入狀態
   */
  private setLoadingState(state: LoadingState, error?: string): void {
    this.optionsStore.update(current => ({
      ...current,
      loading: state,
      error: state === LoadingState.ERROR ? error : undefined
    }));
  }

  /**
   * 清除資料
   */
  clear(): void {
    this.optionsStore.set({
      data: [],
      loading: LoadingState.NOT_LOADED
    });
  }
}

// 匯出單例實例
export const quickOptionsService = QuickOptionsService.getInstance();