/**
 * 統一資料管理器 - 實作單一資料源原則
 * 最後更新: 2025/7/22
 */

import type { UserInfo } from '$lib/auth';
import type {
  GraphQLPharmaceuticalType,
  GraphQLTakeType
} from '$lib/graphql/generated';
import { queryUser } from '$lib/graphql/services';
import { dataStore, dataStoreActions } from '$lib/stores/dataStore';
import { LoadingState } from '$lib/utils/loadingHelpers';
import { get } from 'svelte/store';
import { errorService } from './errorService';
import { pharmaceuticalManagementService } from './pharmaceuticalManagementService';
import type { TakeRecordFilters } from './takeRecordService';
import { takeRecordService } from './takeRecordService';

export class DataManager {
  private static instance: DataManager;
  private loadingPromises = new Map<string, Promise<any>>();

  static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  /**
   * 載入使用者資訊
   */
  async loadUserInfo(forceReload = false): Promise<UserInfo | null> {
    const cacheKey = 'userInfo';

    // 檢查是否正在載入
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // 檢查是否需要載入
    if (!forceReload && dataStoreActions.hasData('userInfo') && !dataStoreActions.needsRefresh('userInfo')) {
      const store = get(dataStore);
      return store.userInfo.data;
    }

    const loadPromise = this.executeLoad(
      cacheKey,
      () => this.fetchUserInfo(),
      (data) => dataStoreActions.setUserInfoData(data),
      () => dataStoreActions.setUserInfoLoading(LoadingState.LOADING),
      (error) => dataStoreActions.setUserInfoLoading(LoadingState.ERROR, error)
    );

    return loadPromise;
  }

  /**
   * 載入藥物清單
   */
  async loadPharmaceuticals(forceReload = false): Promise<GraphQLPharmaceuticalType[] | null> {
    const cacheKey = 'pharmaceuticals';

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    if (!forceReload && dataStoreActions.hasData('pharmaceuticals') && !dataStoreActions.needsRefresh('pharmaceuticals')) {
      const store = get(dataStore);
      return store.pharmaceuticals.data;
    }

    const loadPromise = this.executeLoad(
      cacheKey,
      () => pharmaceuticalManagementService.getPharmaceuticals(),
      (data) => {
        dataStoreActions.setPharmaceuticalsData(data);
        // 同時更新舊的 store 以保持兼容性
      },
      () => dataStoreActions.setPharmaceuticalsLoading(LoadingState.LOADING),
      (error) => dataStoreActions.setPharmaceuticalsLoading(LoadingState.ERROR, error)
    );

    return loadPromise;
  }

  /**
   * 載入服藥記錄
   */
  async loadTakeRecords(filters?: TakeRecordFilters, forceReload = false): Promise<GraphQLTakeType[] | null> {
    const cacheKey = 'takeRecords';

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    if (!forceReload && dataStoreActions.hasData('takeRecords') && !dataStoreActions.needsRefresh('takeRecords')) {
      const store = get(dataStore);
      return store.takeRecords.data;
    }

    const loadPromise = this.executeLoad(
      cacheKey,
      () => takeRecordService.getTakeRecords(filters || { limit: 50 }),
      (data) => {
        dataStoreActions.setTakeRecordsData(data);
        // 同時更新舊的 store 以保持兼容性
        // updateLegacyTakeRecordsStore 已移除
      },
      () => dataStoreActions.setTakeRecordsLoading(LoadingState.LOADING),
      (error) => dataStoreActions.setTakeRecordsLoading(LoadingState.ERROR, error)
    );

    return loadPromise;
  }

  /**
   * 執行載入操作的通用方法
   */
  private async executeLoad<T>(
    cacheKey: string,
    fetcher: () => Promise<T>,
    onSuccess: (data: T) => void,
    onStart: () => void,
    onError: (error: string) => void
  ): Promise<T | null> {
    onStart();

    const loadPromise = errorService.retry(async () => {
      try {
        const data = await fetcher();
        onSuccess(data);
        this.loadingPromises.delete(cacheKey);
        return data;
      } catch (error) {
        this.loadingPromises.delete(cacheKey);
        const errorInfo = errorService.handle(error, `載入${cacheKey}失敗`);
        onError(errorInfo.message);
        throw error;
      }
    }, {
      retries: 3,
      minTimeout: 1000,
      factor: 2,
      onFailedAttempt: (error) => {
        console.warn(`重試載入 ${cacheKey}:`, error.message);
      }
    });

    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      return await loadPromise;
    } catch (error) {
      return null;
    }
  }

  /**
   * 清除所有載入中的 Promise
   */
  clearLoadingPromises(): void {
    this.loadingPromises.clear();
  }

  /**
   * 清除所有資料
   */
  clearAllData(): void {
    this.clearLoadingPromises();
    dataStoreActions.clearAllData();
    // 清理舊的 store
    // clearLegacyStores 已移除
  }

  /**
   * 更新舊的藥物 store（兼容性）
   */

  // 私有方法：實際的 API 呼叫

  private async fetchUserInfo(): Promise<UserInfo> {
    try {
      const user = await queryUser();
      return {
        id: user.id,
        name: user.name,
        email: user.email
      };
    } catch (error) {
      throw new Error(`無法取得使用者資訊: ${error}`);
    }
  }

  /**
   * 刷新資料
   * @param dataType 資料類型
   */
  async refreshData(dataType: 'pharmaceuticals' | 'takeRecords'): Promise<void> {
    switch (dataType) {
      case 'pharmaceuticals':
        await this.loadPharmaceuticals(true);
        break;
      case 'takeRecords':
        await this.loadTakeRecords(undefined, true);
        break;
    }
  }

  /**
   * 事件處理 - 服藥記錄變更時
   */
  async onTakeRecordChanged(): Promise<void> {
    await this.refreshData('takeRecords');
  }

  /**
   * 事件處理 - 藥物變更時
   */
  async onPharmaceuticalChanged(): Promise<void> {
    await this.refreshData('pharmaceuticals');
  }
}

// 匯出單例實例
export const dataManager = DataManager.getInstance();