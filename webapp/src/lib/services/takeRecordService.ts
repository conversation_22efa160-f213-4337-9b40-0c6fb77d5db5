/**
 * 服藥記錄服務 - 專注於服藥記錄相關操作
 * 最後更新: 2025/7/22
 */

import type { Client } from '@urql/svelte';
import { getUrqlClient } from '$lib/graphql/client';
import type { 
  GraphQLTakeType, 
  GraphQLTakeInput, 
  GraphQLTakeUpdateInput
} from '$lib/graphql/generated';
import { errorService } from './errorService';
import { GET_TAKES } from '$lib/graphql/queries/takes';
import { differenceInMinutes, compareDesc, parseISO } from 'date-fns';
import { subMinutes } from 'date-fns';

// 查詢過濾條件型別
export interface TakeRecordFilters {
  pharmaceuticalId?: number;
  fromTime?: string;
  toTime?: string;
  limit?: number;
  offset?: number;
}

// 近期服藥檢查結果
export interface RecentTakeCheck {
  hasRecentTake: boolean;
  lastTakeTime?: string;
  minutesAgo?: number;
}

/**
 * 服藥記錄服務類 - 專注於服藥記錄相關操作
 */
export class TakeRecordService {
  private static instance: TakeRecordService;
  private urqlClient: Client;
  private recentlyAddedTakes = new Map<number, Date>(); // 全域追蹤最近新增的記錄，防止競態條件

  private constructor() {
    this.urqlClient = getUrqlClient();
  }

  /**
   * 取得單例實例
   */
  static getInstance(): TakeRecordService {
    if (!TakeRecordService.instance) {
      TakeRecordService.instance = new TakeRecordService();
    }
    return TakeRecordService.instance;
  }

  /**
   * 取得服藥記錄
   * @param filters 過濾條件
   */
  async getTakeRecords(filters: TakeRecordFilters = {}): Promise<GraphQLTakeType[]> {
    try {
      const result = await this.urqlClient.query(GET_TAKES, filters);
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.getTakes as GraphQLTakeType[];
    } catch (error) {
      const errorInfo = errorService.handle(error, '載入服藥記錄失敗');
      throw new Error(errorInfo.message);
    }
  }

  // 註：getPharmaceuticalTakeRecords 已移除，請直接使用 getTakeRecords

  /**
   * 建立服藥記錄
   * @param input 服藥記錄輸入資料
   */
  async createTakeRecord(input: GraphQLTakeInput): Promise<GraphQLTakeType> {
    try {
      // 驗證輸入
      if (!input.userId || !input.pharmaceuticalId || input.quantity <= 0) {
        throw new Error('用戶ID、藥物ID和劑量為必填項目');
      }

      const takeInput = {
        userId: input.userId,
        pharmaceuticalId: input.pharmaceuticalId,
        quantity: input.quantity,
        takenAt: input.takenAt || new Date().toISOString(),
        notes: input.notes || ''
      };

      const CREATE_TAKE = `
        mutation CreateTake($take: TakeInput!) {
          createTake(take: $take) {
            id
            userId
            pharmaceuticalId
            quantity
            takenAt
            notes
            pharmaceutical {
              id
              name
              dosageUnit
              dosagePerServing
              servingUnitName
            }
          }
        }
      `;

      const result = await this.urqlClient.mutation(CREATE_TAKE, { take: takeInput });
      
      if (result.error) {
        throw result.error;
      }
      
      // 立即追蹤新增的記錄，防止競態條件
      this.recentlyAddedTakes.set(takeInput.pharmaceuticalId, new Date(takeInput.takenAt));
      
      return result.data?.createTake as GraphQLTakeType;
    } catch (error) {
      const errorInfo = errorService.handle(error, '新增服藥紀錄失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 更新服藥記錄
   * @param input 服藥記錄更新資料
   */
  async updateTakeRecord(input: GraphQLTakeUpdateInput): Promise<GraphQLTakeType> {
    try {
      if (!input.id) {
        throw new Error('ID為必填項目');
      }

      const UPDATE_TAKE = `
        mutation UpdateTake($take: TakeUpdateInput!) {
          updateTake(take: $take) {
            id
            userId
            pharmaceuticalId
            quantity
            takenAt
            notes
          }
        }
      `;

      const result = await this.urqlClient.mutation(UPDATE_TAKE, { take: input });
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.updateTake as GraphQLTakeType;
    } catch (error) {
      const errorInfo = errorService.handle(error, '更新服藥紀錄失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 刪除服藥記錄
   * @param id 服藥記錄 ID
   */
  async deleteTakeRecord(id: number): Promise<boolean> {
    try {
      if (!id) {
        throw new Error('ID為必填項目');
      }

      const DELETE_TAKE = `
        mutation DeleteTake($takeId: Int!) {
          deleteTake(takeId: $takeId)
        }
      `;

      const result = await this.urqlClient.mutation(DELETE_TAKE, { takeId: id });
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.deleteTake as boolean;
    } catch (error) {
      const errorInfo = errorService.handle(error, '刪除服藥紀錄失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 檢查30分鐘內是否服用過相同藥物
   * @param pharmaceuticalId 藥物 ID
   */
  async checkRecentTake(pharmaceuticalId: number): Promise<RecentTakeCheck> {
    try {
      const now = new Date();

      // 檢查全域追蹤的最近新增記錄
      const recentlyAdded = this.recentlyAddedTakes.get(pharmaceuticalId);
      if (recentlyAdded) {
        const minutesAgo = Math.abs(differenceInMinutes(now, recentlyAdded));
        if (minutesAgo <= 30) {
          return {
            hasRecentTake: true,
            lastTakeTime: recentlyAdded.toISOString(),
            minutesAgo: Math.round(minutesAgo)
          };
        } else {
          // 超過30分鐘，清除追蹤
          this.recentlyAddedTakes.delete(pharmaceuticalId);
        }
      }

      // 查詢資料庫中的記錄
      const thirtyMinutesAgo = subMinutes(now, 30);
      const filters: TakeRecordFilters = {
        pharmaceuticalId,
        fromTime: thirtyMinutesAgo.toISOString()
      };
      
      const records = await this.getTakeRecords(filters);
      
      if (records.length > 0) {
        // 找出最近的一筆記錄
        const latestRecord = records.sort((a, b) => {
          const d1 = typeof a.takenAt === 'string' ? parseISO(a.takenAt) : a.takenAt;
          const d2 = typeof b.takenAt === 'string' ? parseISO(b.takenAt) : b.takenAt;
          return compareDesc(d1, d2);
        })[0];
        
        const recordTime = typeof latestRecord.takenAt === 'string' ? parseISO(latestRecord.takenAt) : latestRecord.takenAt;
        const minutesAgo = Math.abs(differenceInMinutes(now, recordTime));
        
        return {
          hasRecentTake: true,
          lastTakeTime: latestRecord.takenAt,
          minutesAgo: Math.round(minutesAgo)
        };
      }

      return { hasRecentTake: false };
    } catch (error) {
      console.error('檢查近期服藥紀錄失敗:', error);
      return { hasRecentTake: false };
    }
  }

  /**
   * 快速新增服藥記錄
   * @param pharmaceuticalId 藥物 ID
   * @param quantity 劑量
   * @param notes 備註
   */
  async quickAddTakeRecord(
    pharmaceuticalId: number,
    quantity: number,
    notes?: string
  ): Promise<GraphQLTakeType> {
    // 先驗證藥物是否存在
    const { pharmaceuticalManagementService } = await import('./pharmaceuticalManagementService');
    const exists = await pharmaceuticalManagementService.validatePharmaceuticalExists(pharmaceuticalId);
    
    if (!exists) {
      throw new Error('指定的藥物不存在');
    }

    // 從認證狀態取得使用者 ID
    const { currentUser } = await import('$lib/auth');
    const { get } = await import('svelte/store');
    const user = get(currentUser);
    
    if (!user || !user.id) {
      throw new Error('使用者未登入');
    }

    return this.createTakeRecord({
      userId: user.id,
      pharmaceuticalId,
      quantity,
      takenAt: new Date().toISOString(),
      notes
    });
  }

  /**
   * 批次新增服藥記錄
   * @param inputs 服藥記錄輸入資料陣列
   */
  async batchCreateTakeRecords(inputs: GraphQLTakeInput[]): Promise<(GraphQLTakeType | { error: string })[]> {
    const results = [];

    for (const input of inputs) {
      try {
        const result = await this.createTakeRecord(input);
        results.push(result);
      } catch (error) {
        console.error('批次新增服藥紀錄失敗:', error);
        results.push({ error: error instanceof Error ? error.message : String(error) });
      }
    }

    return results;
  }
}

// 匯出單例實例
export const takeRecordService = TakeRecordService.getInstance();