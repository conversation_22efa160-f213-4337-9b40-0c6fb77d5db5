/**
 * 統一錯誤處理服務
 * 整合原 errorService.ts 和 errorHandler.ts 功能
 * 最後更新: 2025/7/22
 */

import { goto } from '$app/navigation';
import { clearToken } from '$lib/auth';
import pRetry from 'p-retry';

export enum ErrorType {
  /** 網路連線錯誤 */
  NETWORK = 'network',
  /** 認證錯誤 */
  AUTH = 'auth',
  /** 業務邏輯錯誤 */
  BUSINESS = 'business',
  /** 資料驗證錯誤 */
  VALIDATION = 'validation',
  /** 權限錯誤 */
  PERMISSION = 'permission',
  /** 資源不存在錯誤 */
  NOT_FOUND = 'not_found',
  /** 伺服器錯誤 */
  SERVER = 'server',
  /** 請求超時錯誤 */
  TIMEOUT = 'timeout',
  /** 未知錯誤 */
  UNKNOWN = 'unknown'
}

export interface ErrorInfo {
  type: ErrorType;
  message: string;
  code?: string;
  context?: string;
  originalError?: Error;
  timestamp: Date;
  canRetry: boolean;
  details?: any;
}

export class ErrorService {
  private static instance: ErrorService;
  private errorListeners: ((error: ErrorInfo) => void)[] = [];

  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService();
    }
    return ErrorService.instance;
  }

  /**
   * 處理錯誤
   */
  handle(error: unknown, context?: string): ErrorInfo {
    const errorInfo = this.parseError(error, context);
    
    // 記錄錯誤
    console.error(`[${errorInfo.type}] ${errorInfo.message}`, {
      context: errorInfo.context,
      code: errorInfo.code,
      timestamp: errorInfo.timestamp,
      originalError: errorInfo.originalError,
      details: errorInfo.details
    });

    // 特殊錯誤處理
    switch (errorInfo.type) {
      case ErrorType.AUTH:
        this.handleAuthError();
        break;
      case ErrorType.NETWORK:
        // 網路錯誤可能需要特殊處理
        break;
    }

    // 通知錯誤監聽器
    this.notifyListeners(errorInfo);

    return errorInfo;
  }

  /**
   * 解析錯誤類型和訊息
   */
  private parseError(error: unknown, context?: string): ErrorInfo {
    let type = ErrorType.UNKNOWN;
    let message = '未知錯誤';
    let code: string | undefined;
    let canRetry = true;
    let originalError: Error | undefined;
    let details: any;

    const timestamp = new Date();

    // 處理 Error 物件
    if (error instanceof Error) {
      originalError = error;
      message = error.message;
      details = error;

      // 網路錯誤檢測
      if (error instanceof TypeError && error.message.includes('fetch')) {
        type = ErrorType.NETWORK;
        message = '網路連線失敗，請檢查網路狀態';
      } else if (message.includes('網路') || message.includes('連線') || error.name === 'NetworkError') {
        type = ErrorType.NETWORK;
      } else if (message.includes('timeout') || message.includes('超時')) {
        type = ErrorType.TIMEOUT;
      } else if (message.includes('認證') || message.includes('權杖') || message.includes('登入')) {
        type = ErrorType.AUTH;
        canRetry = false;
      } else if (message.includes('驗證') || message.includes('格式')) {
        type = ErrorType.VALIDATION;
        canRetry = false;
      } else {
        type = ErrorType.BUSINESS;
      }
    }
    // 處理 HTTP 狀態錯誤
    else if (error && typeof error === 'object' && 'status' in error) {
      const statusError = error as any;
      code = statusError.status?.toString();
      details = error;

      switch (statusError.status) {
        case 401:
          type = ErrorType.AUTH;
          message = '認證失效，請重新登入';
          canRetry = false;
          break;
        case 403:
          type = ErrorType.PERMISSION;
          message = '權限不足，無法執行此操作';
          canRetry = false;
          break;
        case 404:
          type = ErrorType.NOT_FOUND;
          message = '請求的資源不存在';
          canRetry = false;
          break;
        case 422:
          type = ErrorType.VALIDATION;
          message = '資料驗證失敗，請檢查輸入內容';
          canRetry = false;
          break;
        case 500:
        case 502:
        case 503:
          type = ErrorType.SERVER;
          message = '伺服器發生錯誤，請稍後再試';
          break;
        default:
          type = ErrorType.UNKNOWN;
          message = statusError.message || '發生未知錯誤';
      }
    }
    // 處理 GraphQL 錯誤
    else if (error && typeof error === 'object' && 'graphQLErrors' in error) {
      const gqlError = (error as any).graphQLErrors?.[0];
      if (gqlError) {
        type = ErrorType.VALIDATION;
        message = gqlError.message || 'GraphQL 查詢失敗';
        details = error;
      }
    }
    // 處理字串錯誤
    else if (typeof error === 'string') {
      message = error;
    }

    return {
      type,
      message,
      code,
      context,
      originalError,
      timestamp,
      canRetry,
      details
    };
  }

  /**
   * 處理認證錯誤
   */
  private handleAuthError(): void {
    // 清除認證狀態
    clearToken();
    
    // 跳轉到登入頁面
    goto('/login');
    
    // 可以在這裡顯示通知
    console.warn('認證失效，已自動登出');
  }

  /**
   * 使用 p-retry 的重試機制
   */
  async retry<T>(
    operation: () => Promise<T>,
    options: {
      retries?: number;
      factor?: number;
      minTimeout?: number;
      maxTimeout?: number;
      randomize?: boolean;
      onFailedAttempt?: (error: any) => void;
    } = {}
  ): Promise<T> {
    const {
      retries = 3,
      factor = 2,
      minTimeout = 1000,
      maxTimeout = 30000,
      randomize = true,
      onFailedAttempt
    } = options;

    return pRetry(async (attemptCount) => {
      try {
        return await operation();
      } catch (error) {
        const errorInfo = this.parseError(error);
        
        // 如果錯誤不可重試，立即拋出
        if (!errorInfo.canRetry) {
          const abortError = new Error(errorInfo.message);
          abortError.name = 'AbortError';
          throw abortError;
        }

        // 執行失敗回調
        if (onFailedAttempt) {
          onFailedAttempt({ ...errorInfo, attemptNumber: attemptCount });
        }

        console.warn(`操作失敗，正在重試 (${attemptCount}/${retries + 1})`, errorInfo.message);
        
        throw error;
      }
    }, {
      retries,
      factor,
      minTimeout,
      maxTimeout,
      randomize
    });
  }

  /**
   * 添加錯誤監聽器
   */
  addErrorListener(listener: (error: ErrorInfo) => void): () => void {
    this.errorListeners.push(listener);
    
    // 返回移除監聽器的函數
    return () => {
      const index = this.errorListeners.indexOf(listener);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  /**
   * 移除錯誤監聽器
   */
  removeErrorListener(listener: (error: ErrorInfo) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有錯誤監聽器
   */
  private notifyListeners(error: ErrorInfo): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.error('錯誤監聽器執行失敗:', err);
      }
    });
  }

  /**
   * 創建帶 timeout 的 fetch
   */
  async fetchWithTimeout(url: string, options: RequestInit = {}, timeoutMs: number = 30000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`請求超時 (${timeoutMs}ms)`);
      }
      throw error;
    }
  }

  /**
   * 獲取使用者友善的錯誤訊息
   */
  getUserFriendlyMessage(error: ErrorInfo): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return '網路連線有問題，請檢查網路設定後重試';
      case ErrorType.AUTH:
        return '登入已過期，請重新登入';
      case ErrorType.PERMISSION:
        return '您沒有權限執行此操作';
      case ErrorType.VALIDATION:
        return '輸入的資料有誤，請檢查後重試';
      case ErrorType.NOT_FOUND:
        return '找不到請求的資料';
      case ErrorType.SERVER:
        return '伺服器暫時無法處理請求，請稍後再試';
      case ErrorType.BUSINESS:
        return error.message || '操作失敗，請重試';
      case ErrorType.TIMEOUT:
        return '請求超時，請檢查網路連線後重試';
      default:
        return '發生未預期的錯誤，請聯繫技術支援';
    }
  }
}

// 匯出單例實例
export const errorService = ErrorService.getInstance();

// 便利函數
export function handleError(error: unknown, context?: string): ErrorInfo {
  return errorService.handle(error, context);
}

export function addErrorListener(listener: (error: ErrorInfo) => void): () => void {
  return errorService.addErrorListener(listener);
}