/**
 * 藥物管理服務 - 專注於藥物相關操作
 * 最後更新: 2025/7/22
 */

import type { Client } from '@urql/svelte';
import { getUrqlClient } from '$lib/graphql/client';
import type { 
  GraphQLPharmaceuticalType, 
  GraphQLPharmaceuticalInput, 
  GraphQLPharmaceuticalUpdateInput
} from '$lib/graphql/generated';
import { errorService } from './errorService';
import { 
  GET_PHARMACEUTICALS, 
  CREATE_PHARMACEUTICAL, 
  UPDATE_PHARMACEUTICAL, 
  DELETE_PHARMACEUTICAL 
} from '$lib/graphql/queries/pharmaceuticals';

/**
 * 藥物管理服務類 - 專注於藥物相關操作
 */
export class PharmaceuticalManagementService {
  private static instance: PharmaceuticalManagementService;
  private urqlClient: Client;

  private constructor() {
    this.urqlClient = getUrqlClient();
  }

  /**
   * 取得單例實例
   */
  static getInstance(): PharmaceuticalManagementService {
    if (!PharmaceuticalManagementService.instance) {
      PharmaceuticalManagementService.instance = new PharmaceuticalManagementService();
    }
    return PharmaceuticalManagementService.instance;
  }

  /**
   * 取得藥物清單
   * @param currentPlanOnly 是否只取得治療中的藥物
   */
  async getPharmaceuticals(currentPlanOnly?: boolean): Promise<GraphQLPharmaceuticalType[]> {
    try {
      const result = await this.urqlClient.query(GET_PHARMACEUTICALS, { currentPlanOnly });
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.getPharmaceuticals as GraphQLPharmaceuticalType[];
    } catch (error) {
      const errorInfo = errorService.handle(error, '載入藥物清單失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 取得藥物清單（向後相容格式）
   * @param currentPlanOnly 是否只取得治療中的藥物
   */
  // 註：getAvailablePharmaceuticals 已移除，請直接使用 getPharmaceuticals

  /**
   * 建立藥物
   * @param input 藥物輸入資料
   */
  async createPharmaceutical(input: GraphQLPharmaceuticalInput): Promise<GraphQLPharmaceuticalType> {
    try {
      const result = await this.urqlClient.mutation(CREATE_PHARMACEUTICAL, { pharmaceutical: input });
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.createPharmaceutical as GraphQLPharmaceuticalType;
    } catch (error) {
      const errorInfo = errorService.handle(error, '建立藥物失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 更新藥物
   * @param input 藥物更新資料
   */
  async updatePharmaceutical(input: GraphQLPharmaceuticalUpdateInput): Promise<GraphQLPharmaceuticalType> {
    try {
      const result = await this.urqlClient.mutation(UPDATE_PHARMACEUTICAL, { pharmaceutical: input });
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.updatePharmaceutical as GraphQLPharmaceuticalType;
    } catch (error) {
      const errorInfo = errorService.handle(error, '更新藥物失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 刪除藥物
   * @param id 藥物 ID
   */
  async deletePharmaceutical(id: number): Promise<boolean> {
    try {
      const result = await this.urqlClient.mutation(DELETE_PHARMACEUTICAL, { id });
      
      if (result.error) {
        throw result.error;
      }
      
      return result.data?.deletePharmaceutical as boolean;
    } catch (error) {
      const errorInfo = errorService.handle(error, '刪除藥物失敗');
      throw new Error(errorInfo.message);
    }
  }

  /**
   * 驗證藥物是否存在
   * @param pharmaceuticalId 藥物 ID
   */
  async validatePharmaceuticalExists(pharmaceuticalId: number): Promise<boolean> {
    try {
      const pharmaceuticals = await this.getPharmaceuticals();
      return pharmaceuticals.some(p => p.id === pharmaceuticalId);
    } catch (error) {
      console.error('驗證藥物失敗:', error);
      return false;
    }
  }
}

// 匯出單例實例
export const pharmaceuticalManagementService = PharmaceuticalManagementService.getInstance();