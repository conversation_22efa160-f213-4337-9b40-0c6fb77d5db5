/**
 * Skeleton UI 主題管理 Store
 * 管理多個主題的切換和持久化存儲
 */

import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// 可用的主題列表
export const availableThemes = [
  'cerberus',
  'modern', 
  'rocket',
  'vintage',
  'seafoam',
  'hamlindigo',
  'crimson'
] as const;

export type Theme = typeof availableThemes[number];

// 默認主題
const DEFAULT_THEME: Theme = 'cerberus';
const STORAGE_KEY = 'skeleton-theme';

// 從 localStorage 讀取主題
function getStoredTheme(): Theme {
  if (!browser) return DEFAULT_THEME;
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored && availableThemes.includes(stored as Theme)) {
      return stored as Theme;
    }
  } catch (error) {
    console.warn('無法讀取主題設定:', error);
  }
  
  return DEFAULT_THEME;
}

// 應用主題到 DOM
function applyTheme(theme: Theme): void {
  if (!browser) return;
  
  try {
    // 更新 data-theme 屬性
    document.documentElement.setAttribute('data-theme', theme);
    
    // 儲存到 localStorage
    localStorage.setItem(STORAGE_KEY, theme);
  } catch (error) {
    console.warn('無法應用主題:', error);
  }
}

// 創建主題 store
function createThemeStore() {
  const { subscribe, set, update } = writable<Theme>(getStoredTheme());

  return {
    subscribe,
    
    // 設定主題
    setTheme: (newTheme: Theme) => {
      if (!availableThemes.includes(newTheme)) {
        console.warn(`不支援的主題: ${newTheme}`);
        return;
      }
      
      applyTheme(newTheme);
      set(newTheme);
    },
    
    // 切換到下一個主題
    nextTheme: () => {
      update(currentTheme => {
        const currentIndex = availableThemes.indexOf(currentTheme);
        const nextIndex = (currentIndex + 1) % availableThemes.length;
        const nextTheme = availableThemes[nextIndex];
        
        applyTheme(nextTheme);
        return nextTheme;
      });
    },
    
    // 初始化主題
    init: () => {
      if (!browser) return;
      
      const storedTheme = getStoredTheme();
      applyTheme(storedTheme);
      set(storedTheme);
    }
  };
}

export const themeStore = createThemeStore();