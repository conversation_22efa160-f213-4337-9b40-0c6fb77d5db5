/**
 * 服藥記錄操作 - 向後相容層
 * 最後更新: 2025/7/22
 *
 * 此檔案提供向後相容的 API，內部使用新的服務實現
 */

import { takeRecordService } from '$lib/services/takeRecordService';
import { dataManager } from '$lib/services/dataManager';
import type { GraphQLTakeInput, GraphQLTakeUpdateInput } from '$lib/graphql/generated';

/**
 * 新增服藥記錄（向後相容）
 */
export async function addPharmaceuticalTake(input: GraphQLTakeInput): Promise<any> {
  try {
    const takeInput: GraphQLTakeInput = {
      userId: input.userId,
      pharmaceuticalId: input.pharmaceuticalId,
      quantity: input.quantity,
      takenAt: input.takenAt,
      notes: input.notes
    };

    const result = await takeRecordService.createTakeRecord(takeInput);

    // 觸發資料更新
    await dataManager.onTakeRecordChanged();

    return result;
  } catch (error) {
    console.error('新增服藥紀錄失敗:', error);
    throw error;
  }
}

/**
 * 更新服藥記錄（向後相容）
 */
export async function updatePharmaceuticalTake(input: GraphQLTakeUpdateInput): Promise<any> {
  try {
    const takeInput: GraphQLTakeUpdateInput = {
      id: input.id,
      quantity: input.quantity,
      takenAt: input.takenAt,
      notes: input.notes
    };

    const result = await takeRecordService.updateTakeRecord(takeInput);

    // 觸發資料更新
    await dataManager.onTakeRecordChanged();

    return result;
  } catch (error) {
    console.error('更新服藥紀錄失敗:', error);
    throw error;
  }
}

/**
 * 刪除服藥記錄（向後相容）
 */
export async function deletePharmaceuticalTake(id: number): Promise<boolean> {
  try {
    const success = await takeRecordService.deleteTakeRecord(id);

    if (success) {
      // 觸發資料更新
      await dataManager.onTakeRecordChanged();
    }

    return success;
  } catch (error) {
    console.error('刪除服藥紀錄失敗:', error);
    throw error;
  }
}

/**
 * 檢查30分鐘內是否服用過相同藥物（向後相容）
 */
export async function checkRecentTake(pharmaceuticalId: number): Promise<{
  hasRecentTake: boolean;
  lastTakeTime?: string;
  minutesAgo?: number;
}> {
  try {
    return await takeRecordService.checkRecentTake(pharmaceuticalId);
  } catch (error) {
    console.error('檢查近期服藥紀錄失敗:', error);
    return { hasRecentTake: false };
  }
}


/**
 * 快速新增服藥記錄（向後相容）
 */
export async function quickAddPharmaceutical(
  pharmaceuticalId: number,
  quantity: number,
  notes?: string
): Promise<any> {
  try {
    const result = await takeRecordService.quickAddTakeRecord(pharmaceuticalId, quantity, notes);

    // 觸發資料更新
    await dataManager.onTakeRecordChanged();

    return result;
  } catch (error) {
    console.error('快速新增服藥紀錄失敗:', error);
    throw error;
  }
}

/**
 * 批次新增服藥記錄（向後相容）
 */
export async function batchAddPharmaceuticalTakes(
  inputs: GraphQLTakeInput[]
): Promise<any[]> {
  try {
    const takeInputs: GraphQLTakeInput[] = inputs.map(input => ({
      userId: input.userId,
      pharmaceuticalId: input.pharmaceuticalId,
      quantity: input.quantity,
      takenAt: input.takenAt,
      notes: input.notes
    }));

    const results = await takeRecordService.batchCreateTakeRecords(takeInputs);

    // 觸發資料更新
    await dataManager.onTakeRecordChanged();

    return results;
  } catch (error) {
    console.error('批次新增服藥紀錄失敗:', error);
    throw error;
  }
}