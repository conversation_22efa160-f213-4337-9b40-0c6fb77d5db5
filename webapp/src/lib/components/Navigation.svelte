<script>
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { logout } from '$lib/auth';
  import { userInfoStore } from '$lib/stores/dataStore';
  import { themeStore } from '$lib/stores/themeStore';
  import { fade } from 'svelte/transition';
  import { onMount } from 'svelte';

  let isUserMenuOpen = false;

  function handleLogout() {
    logout();
  }

  function handleThemeToggle() {
    themeStore.nextTheme();
  }

  // 初始化主題
  onMount(() => {
    themeStore.init();
  });

  $: currentPath = $page.url.pathname;
  $: currentTheme = $themeStore;
  $: currentUser = $userInfoStore.data;
</script>

<header class="fixed top-0 left-0 right-0 z-50 bg-surface-100/80 dark:bg-surface-800/80 backdrop-blur-lg shadow-lg border-b border-surface-200/50 dark:border-surface-700/50">
  <nav class="container mx-auto px-4">
    <div class="flex justify-between items-center h-16">
      <!-- Logo / Brand -->
      <div class="flex items-center">
        <a href="/whendiditake" class="text-xl font-bold text-primary-700 dark:text-primary-400 hover:text-primary-500 transition-colors">
          <span class="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
            When Did I Take
          </span>
        </a>
      </div>

      <!-- Navigation - 響應式設計 -->
      <div class="flex items-center space-x-2 md:space-x-4">
        <a
          href="/whendiditake"
          class="btn {currentPath === '/whendiditake' ? 'variant-filled-primary' : 'variant-ghost-surface'}"
        >
          <iconify-icon icon="lucide:home" class="text-lg"></iconify-icon>
          <span class="hidden md:inline">首頁</span>
        </a>
        <a
          href="/pharmaceuticals"
          class="btn {currentPath.startsWith('/pharmaceuticals') ? 'variant-filled-primary' : 'variant-ghost-surface'}"
        >
          <iconify-icon icon="lucide:pill" class="text-lg"></iconify-icon>
          <span class="hidden md:inline">藥物管理</span>
        </a>
      </div>

      <!-- User Menu -->
      <div class="flex items-center">
        {#if currentUser}
          <div class="relative">
            <button
              class="btn-icon bg-primary-600 text-white hover:bg-primary-700 w-10 h-10"
              on:click={() => isUserMenuOpen = !isUserMenuOpen}
            >
              <span class="text-sm font-bold">
                {currentUser.name.charAt(0).toUpperCase()}
              </span>
            </button>

            {#if isUserMenuOpen}
              <div 
                class="card absolute right-0 top-12 w-64 p-4 shadow-xl z-10"
                transition:fade={{ duration: 200 }}
              >
                <div class="flex items-center space-x-3 pb-3 border-b border-surface-300 dark:border-surface-600">
                  <div class="btn-icon bg-primary-600 text-white w-12 h-12">
                    <span class="text-lg font-bold">
                      {currentUser.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p class="font-semibold text-surface-800 dark:text-surface-100">{currentUser.name}</p>
                    <p class="text-sm text-surface-500 dark:text-surface-400">ID: {currentUser.id}</p>
                  </div>
                </div>
                <div class="pt-3 space-y-3">
                  <p class="text-sm text-surface-500 dark:text-surface-400">{currentUser.email}</p>
                  
                  <!-- 主題切換 -->
                  <div class="border-t border-surface-300 dark:border-surface-600 pt-3">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-surface-700 dark:text-surface-300">主題設定</span>
                      <iconify-icon 
                        icon="lucide:palette" 
                        class="text-sm text-surface-500"
                      ></iconify-icon>
                    </div>
                    <div class="flex flex-col gap-1 p-2 bg-surface-100 dark:bg-surface-700 rounded-lg min-w-[120px]">
                      <div class="text-xs text-surface-600 dark:text-surface-400 mb-1">選擇主題</div>
                      <button
                        class="btn text-xs px-2 py-1 {currentTheme === 'cerberus' ? 'variant-filled-primary' : 'variant-ghost-surface'} justify-start"
                        on:click={() => themeStore.setTheme('cerberus')}
                      >
                        <span>Cerberus</span>
                      </button>
                      <button
                        class="btn text-xs px-2 py-1 {currentTheme === 'modern' ? 'variant-filled-primary' : 'variant-ghost-surface'} justify-start"
                        on:click={() => themeStore.setTheme('modern')}
                      >
                        <span>Modern</span>
                      </button>
                      <button
                        class="btn text-xs px-2 py-1 {currentTheme === 'rocket' ? 'variant-filled-primary' : 'variant-ghost-surface'} justify-start"
                        on:click={() => themeStore.setTheme('rocket')}
                      >
                        <span>Rocket</span>
                      </button>
                      <button
                        class="btn text-xs px-2 py-1 {currentTheme === 'vintage' ? 'variant-filled-primary' : 'variant-ghost-surface'} justify-start"
                        on:click={() => themeStore.setTheme('vintage')}
                      >
                        <span>Vintage</span>
                      </button>
                    </div>
                  </div>
                  
                  <!-- 登出按鈕 -->
                  <button
                    class="btn variant-filled-error w-full"
                    on:click={handleLogout}
                  >
                    <iconify-icon icon="lucide:log-out" class="text-lg"></iconify-icon>
                    <span>登出</span>
                  </button>
                </div>
              </div>
            {/if}
          </div>
        {/if}
      </div>
    </div>

  </nav>
</header>


<!-- Click outside to close user menu -->
<svelte:window on:click={(e) => {
  if (!e.target.closest('.relative')) {
    isUserMenuOpen = false;
  }
}} />