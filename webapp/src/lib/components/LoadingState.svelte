<!--
  載入狀態組件 - 使用 Skeleton UI
  最後更新: 2025/7/23
-->
<script lang="ts">
  import { LoadingState, shouldShowLoader, shouldShowSkeleton, hasData, hasError } from '$lib/utils/loadingHelpers';
  
  export let state: LoadingState;
  export let error: string | undefined = undefined;
  export let showRetry: boolean = true;
  export let retryCallback: (() => void) | undefined = undefined;
  export let skeletonType: 'list' | 'card' | 'table' = 'list';
  export let itemCount: number = 5;
</script>

{#if shouldShowSkeleton(state)}
  <!-- Skeleton UI 骨架屏 -->
  <div class="space-y-4">
    {#if skeletonType === 'list'}
      {#each Array(itemCount) as _, i}
        <div class="card p-4 space-y-4">
          <div class="flex items-center space-x-4">
            <div class="placeholder-circle animate-pulse w-12 h-12"></div>
            <div class="flex-1 space-y-2">
              <div class="placeholder animate-pulse w-3/4 h-4"></div>
              <div class="placeholder animate-pulse w-1/2 h-3"></div>
            </div>
            <div class="placeholder animate-pulse w-8 h-8"></div>
          </div>
        </div>
      {/each}
    {:else if skeletonType === 'card'}
      {#each Array(itemCount) as _, i}
        <div class="card p-6 space-y-4">
          <div class="placeholder animate-pulse w-1/3 h-6"></div>
          <div class="space-y-2">
            <div class="placeholder animate-pulse w-full h-4"></div>
            <div class="placeholder animate-pulse w-5/6 h-4"></div>
          </div>
        </div>
      {/each}
    {:else if skeletonType === 'table'}
      <div class="space-y-2">
        {#each Array(itemCount) as _, i}
          <div class="flex space-x-4 p-3 card">
            <div class="placeholder animate-pulse w-1/4 h-4"></div>
            <div class="placeholder animate-pulse w-1/3 h-4"></div>
            <div class="placeholder animate-pulse w-1/6 h-4"></div>
            <div class="placeholder animate-pulse w-1/5 h-4"></div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
{:else if shouldShowLoader(state)}
  <!-- Skeleton UI 載入指示器 -->
  <div class="flex flex-col items-center justify-center py-12 space-y-4">
    <div class="relative">
      <div class="w-12 h-12 border-4 border-surface-300 border-t-primary-500 rounded-full animate-spin"></div>
    </div>
    <p class="text-sm text-surface-600">載入中...</p>
  </div>
{:else if hasError(state)}
  <!-- Skeleton UI 錯誤狀態 -->
  <div class="flex flex-col items-center justify-center py-12 space-y-4">
    <div class="flex items-center space-x-2 text-error-500">
      <iconify-icon icon="lucide:alert-circle" class="text-2xl"></iconify-icon>
      <span class="text-lg font-medium">載入失敗</span>
    </div>
    
    {#if error}
      <p class="text-sm text-surface-500 text-center max-w-md">
        {error}
      </p>
    {/if}
    
    {#if showRetry && retryCallback}
      <button
        class="btn variant-filled-primary"
        on:click={retryCallback}
      >
        <iconify-icon icon="lucide:refresh-cw" class="text-lg mr-2"></iconify-icon>
        重試
      </button>
    {/if}
  </div>
{:else if state === LoadingState.NOT_LOADED}
  <!-- Skeleton UI 尚未載入狀態 -->
  <div class="flex flex-col items-center justify-center py-12 space-y-4">
    <div class="text-surface-400">
      <iconify-icon icon="lucide:database" class="text-4xl"></iconify-icon>
    </div>
    <p class="text-sm text-surface-600">等待載入資料</p>
  </div>
{/if}