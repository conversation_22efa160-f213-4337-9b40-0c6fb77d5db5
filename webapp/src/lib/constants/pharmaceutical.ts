/**
 * 藥物相關常數定義
 * 最後更新: 2025/7/22
 * 
 * 統一管理藥物劑型、單位等常數，遵循單一來源原則
 */

/**
 * 藥物劑型定義（陣列格式，用於表單選項）
 */
export const PHARMACEUTICAL_FORMULATIONS = [
  { value: 'CAPSULE', label: '膠囊' },
  { value: 'PILL', label: '藥丸' },
  { value: 'INJECTION', label: '注射劑' },
  { value: 'POWDER', label: '粉劑' },
  { value: 'SYRUP', label: '糖漿' },
  { value: 'PATCH', label: '貼片' },
  { value: 'SPRAY', label: '噴霧' },
  { value: 'TOPICAL', label: '外用藥' }
] as const;

/**
 * 藥物劑型對應表（物件格式，用於顯示轉換）
 */
export const PHARMACEUTICAL_FORMULATION_MAP = Object.fromEntries(
  PHARMACEUTICAL_FORMULATIONS.map(f => [f.value, f.label])
) as Record<string, string>;

/**
 * 常見劑量單位
 */
export const COMMON_DOSAGE_UNITS = [
  'mg', 'g', 'ml', 'IU', 'mcg', 'unit'
] as const;

/**
 * 常用服用單位
 */
export const COMMON_SERVING_UNITS = [
  '份',
  '顆',
  '粒',
  '錠',
  '包',
  '瓶',
  '支',
  '滴'
] as const;

/**
 * 藥物劑型類型定義
 */
export type PharmaceuticalFormulationType = typeof PHARMACEUTICAL_FORMULATIONS[number]['value'];

/**
 * 劑量單位類型定義
 */
export type DosageUnitType = typeof COMMON_DOSAGE_UNITS[number];

/**
 * 服用單位類型定義
 */
export type ServingUnitType = typeof COMMON_SERVING_UNITS[number];