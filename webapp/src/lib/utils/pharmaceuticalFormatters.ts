/**
 * 藥物格式化工具函數
 * 最後更新: 2025/7/22
 */

/**
 * 取得服用單位顯示名稱
 * @param servingUnitName 服用單位名稱
 * @returns 顯示用的單位名稱
 */
export function getServingUnitDisplay(servingUnitName?: string | null): string {
  return servingUnitName || '份';
}

/**
 * 格式化劑量顯示
 * @param absoluteDosage 絕對劑量
 * @param dosagePerServing 每份劑量
 * @param dosageUnit 劑量單位
 * @param servingUnitName 服用單位名稱
 * @returns 格式化後的劑量顯示字串
 */
export function formatDosageDisplay(
  absoluteDosage: number, 
  dosagePerServing: number, 
  dosageUnit: string, 
  servingUnitName?: string | null
): string {
  const servings = absoluteDosage / dosagePerServing;
  const unitDisplay = getServingUnitDisplay(servingUnitName);
  
  if (servings === 1) {
    return `${absoluteDosage} ${dosageUnit} (1 ${unitDisplay})`;
  } else {
    return `${absoluteDosage} ${dosageUnit} (${servings} ${unitDisplay})`;
  }
}

/**
 * 格式化每日總量顯示
 * @param count 服用次數
 * @param totalServings 總服用份數
 * @param totalDosage 總劑量
 * @param dosageUnit 劑量單位
 * @param servingUnitName 服用單位名稱
 * @returns 格式化後的每日總量顯示字串
 */
export function formatDailyTotalDisplay(
  count: number, 
  totalServings: number, 
  totalDosage: number, 
  dosageUnit: string, 
  servingUnitName?: string | null
): string {
  const unitDisplay = getServingUnitDisplay(servingUnitName);
  
  if (count === 1) {
    return `今日 1 次，${totalDosage} ${dosageUnit} (${totalServings} ${unitDisplay})`;
  } else {
    return `今日 ${count} 次，${totalDosage} ${dosageUnit} (${totalServings} ${unitDisplay})`;
  }
}